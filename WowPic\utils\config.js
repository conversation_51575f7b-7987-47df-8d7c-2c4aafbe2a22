// API服务器基础URL
export const baseUrl = 'https://wowpic.hxlsf.com';
// export const baseUrl = 'http://localhost:8000';

// 图片服务器URL
export const imageUrl = 'https://wowpic.hxlsf.com/static';
// export const imageUrl = 'http://localhost:8000/static';

// 默认头像
export const defaultAvatar = '/static/images/default-avatar.png';

// 每页数据条数
export const pageSize = 10;

// 上传文件大小限制(10MB)
export const maxUploadSize = 10 * 1024 * 1024;

// 允许上传的图片类型
export const allowedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];

// 轮询生成结果的间隔(毫秒)
export const pollingInterval = 2000;

// 订阅消息模板ID占位符（已由前端在运行时从后台获取，不再在此配置）
export const subscriptionTemplateId = '';

/**
 * 处理图片URL，确保能够正确显示
 * @param {string} url - 图片URL，可能是相对路径或绝对路径
 * @returns {string} 处理后的URL
 */
export const getImageUrl = (url) => {
  if (!url) return '';
  
  // 如果已经是完整URL，直接返回
  if (url.startsWith('http')) {
    return url;
  }
  
  // 确保url以/开头
  const path = url.startsWith('/') ? url : `/${url}`;
  
  // 如果是以/static开头，则添加baseUrl前缀
  if (path.startsWith('/static')) {
    return `${baseUrl}${path}`;
  }
  
  // 其他情况，可能是相对路径，也添加baseUrl
  return `${baseUrl}${path}`;
}; 