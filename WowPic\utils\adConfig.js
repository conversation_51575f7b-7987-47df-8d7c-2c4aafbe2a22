// 广告配置文件
export const adConfig = {
  // 激励视频广告配置
  rewardedVideo: {
    // 广告位ID - 生产环境需要替换为真实的广告位ID
    adUnitId: 'adunit-b8fe48de4a45e35e',
    
    // 重试配置
    maxRetries: 2,
    retryDelay: 1000, // 重试延迟(毫秒)
    
    // 超时配置
    loadTimeout: 10000, // 加载超时(毫秒)
    showTimeout: 10000, // 显示超时(毫秒)
    
    // 奖励配置
    reward: 15, // 每次观看奖励
    maxWatchPerDay: 3, // 每日最大观看次数
  },
  
  // 环境检测
  isSupported() {
    return typeof wx !== 'undefined' && wx.createRewardedVideoAd
  },
  
  // 获取当前环境信息
  getEnvironmentInfo() {
    const info = {
      platform: 'unknown',
      isDevTool: false,
      supportAd: false
    }
    
    try {
      if (typeof wx !== 'undefined') {
        info.platform = 'wechat'
        info.supportAd = !!wx.createRewardedVideoAd
        
        // 检测是否在开发者工具中
        const systemInfo = wx.getSystemInfoSync()
        info.isDevTool = systemInfo.platform === 'devtools'
      }
    } catch (error) {
      console.warn('获取环境信息失败:', error)
    }
    
    return info
  },
  
  // 日志配置
  enableDebugLog: true, // 是否启用调试日志
  
  // 错误处理配置
  errorHandling: {
    // 常见错误码及处理方式
    errorCodes: {
      1000: '后端接口调用失败',
      1001: '参数错误',
      1002: '广告单元无效',
      1003: '内部错误',
      1004: '无合适的广告',
      1005: '广告组件审核中',
      1006: '广告组件被驳回',
      1007: '广告组件被封禁',
      1008: '广告单元已关闭',
      1009: '广告单元切量',
      1010: '不允许展示开屏广告',
      1011: '广告单元配置错误',
      1012: '广告单元不可用',
      1013: '广告单元已过期',
      2001: '网络错误',
      2002: '网络超时',
      2003: '网络中断',
      2004: '网络异常'
    },
    
    // 获取错误描述
    getErrorMessage(errCode) {
      return this.errorCodes[errCode] || `未知错误(${errCode})`
    }
  }
}

// 广告管理器类
export class AdManager {
  constructor(config = adConfig.rewardedVideo) {
    this.config = config
    this.videoAd = null
    this.isInitialized = false
    this.retryCount = 0
    this.listeners = {
      onLoad: [],
      onError: [],
      onClose: []
    }
  }
  
  // 初始化广告
  init() {
    if (!adConfig.isSupported()) {
      console.warn('当前环境不支持激励视频广告')
      return false
    }
    
    try {
      // 销毁旧实例
      this.destroy()
      
      // 创建新实例
      this.videoAd = wx.createRewardedVideoAd({
        adUnitId: this.config.adUnitId
      })
      
      // 绑定事件
      this.bindEvents()
      
      this.isInitialized = true
      this.retryCount = 0
      
      if (adConfig.enableDebugLog) {
        console.log('广告管理器初始化成功')
      }
      
      return true
    } catch (error) {
      console.error('广告管理器初始化失败:', error)
      this.isInitialized = false
      return false
    }
  }
  
  // 绑定事件监听器
  bindEvents() {
    if (!this.videoAd) return
    
    this.videoAd.onLoad(() => {
      if (adConfig.enableDebugLog) {
        console.log('激励视频广告加载成功')
      }
      this.listeners.onLoad.forEach(callback => callback())
    })
    
    this.videoAd.onError((err) => {
      console.error('激励视频广告错误:', err)
      this.listeners.onError.forEach(callback => callback(err))
    })
    
    this.videoAd.onClose((res) => {
      if (adConfig.enableDebugLog) {
        console.log('激励视频广告关闭:', res)
      }
      this.listeners.onClose.forEach(callback => callback(res))
    })
  }
  
  // 添加事件监听器
  on(event, callback) {
    if (this.listeners[event]) {
      this.listeners[event].push(callback)
    }
  }
  
  // 移除事件监听器
  off(event, callback) {
    if (this.listeners[event]) {
      const index = this.listeners[event].indexOf(callback)
      if (index > -1) {
        this.listeners[event].splice(index, 1)
      }
    }
  }
  
  // 显示广告
  async show() {
    if (!this.isValid()) {
      throw new Error('广告实例无效')
    }
    
    try {
      await this.videoAd.show()
      return true
    } catch (error) {
      // 如果是实例被销毁的错误，尝试重新初始化
      if (error.errMsg && error.errMsg.includes('destroyed')) {
        if (this.retryCount < this.config.maxRetries) {
          this.retryCount++
          console.log(`广告实例被销毁，尝试重新初始化 (${this.retryCount}/${this.config.maxRetries})`)
          
          if (this.init()) {
            await new Promise(resolve => setTimeout(resolve, this.config.retryDelay))
            return await this.show()
          }
        }
      }
      throw error
    }
  }
  
  // 检查实例是否有效
  isValid() {
    return this.isInitialized && 
           this.videoAd && 
           typeof this.videoAd.show === 'function' && 
           typeof this.videoAd.load === 'function'
  }
  
  // 销毁广告实例
  destroy() {
    if (this.videoAd) {
      try {
        // 移除事件监听器
        this.videoAd.offLoad && this.videoAd.offLoad()
        this.videoAd.offError && this.videoAd.offError()
        this.videoAd.offClose && this.videoAd.offClose()
        
        // 销毁实例
        this.videoAd.destroy && this.videoAd.destroy()
        
        if (adConfig.enableDebugLog) {
          console.log('广告实例已销毁')
        }
      } catch (error) {
        console.error('销毁广告实例时出错:', error)
      }
    }
    
    this.videoAd = null
    this.isInitialized = false
    this.retryCount = 0
    
    // 清空监听器
    Object.keys(this.listeners).forEach(key => {
      this.listeners[key] = []
    })
  }
}

export default adConfig
